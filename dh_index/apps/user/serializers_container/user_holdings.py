from dh_index.apps.user.models import User, UserHoldings
from dh_index.apps.user.serializers_container import serializers


class UserHoldingsSerializers(serializers.ModelSerializer):
    class Meta:
        model = UserHoldings
        fields = '__all__'

        extra_kwargs = {'created_at': {'write_only': True}, 'updated_at': {'write_only': True}}


class UserHoldingsCreateSerializer(serializers.ModelSerializer):
    def create(self, validated_data):
        current_user = self.context['request'].user
        validated_data['user_id'] = current_user.id
        user_holdings = UserHoldings.objects.create(**validated_data)
        return user_holdings


class UserHoldingsUpdateSerializer(serializers.ModelSerializer):
    def update(self, instance, validated_data):
        for field, value in validated_data.items():
            setattr(instance, field, value)
        instance.save()
        return instance
